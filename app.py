import os
import re
import logging
import random
from flask import Flask, request, jsonify, send_file
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from datetime import datetime
import uuid
import sqlite3

app = Flask(__name__)

# Configuration
DB_FILE = 'voip.db'
API_KEY = os.getenv('VOIP_API_KEY', 'secret-voip-key')  # Use env var for security
HOST = os.getenv('FLASK_HOST', '0.0.0.0')
PORT = int(os.getenv('FLASK_PORT', 5000))
``
# Logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')

# Rate limiting (comment out if flask-limiter causes issues)
limiter = Limiter(get_remote_address, app=app, storage_uri="memory://", default_limits=["100 per day", "10 per minute"])

# Initialize DB
def init_db():
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    cursor.execute('''CREATE TABLE IF NOT EXISTS call_logs 
                      (call_id TEXT PRIMARY KEY, phone_number TEXT, start_time TEXT, duration INTEGER)''')
    cursor.execute('''CREATE TABLE IF NOT EXISTS settings 
                      (key TEXT PRIMARY KEY, value TEXT)''')
    cursor.execute('INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)', ('call_forwarding', ''))
    conn.commit()
    conn.close()

init_db()

# Validate phone number
def validate_phone(phone):
    pattern = r'^\+?\d{10,15}$'
    return bool(re.match(pattern, phone))

# Check API key
def check_api_key():
    return request.headers.get('X-API-Key') == API_KEY

@app.route('/api/call', methods=['POST'])
@limiter.limit("5 per minute")  # Comment this line if flask-limiter fails
def initiate_call():
    if not check_api_key():
        return jsonify({'error': 'Unauthorized'}), 401
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        if not phone_number or not validate_phone(phone_number):
            return jsonify({'error': 'Valid phone number is required (e.g., +1234567890)'}), 400
        
        # Get forwarding number
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('SELECT value FROM settings WHERE key = ?', ('call_forwarding',))
        forwarding = cursor.fetchone()[0]
        conn.close()
        
        target_number = forwarding if forwarding else phone_number
        # Simulate call initiation (in production, integrate with Asterisk/Kamailio)
        call_id = str(uuid.uuid4())
        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        duration = random.randint(10, 300)  # Simulated; in production, update post-call
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('INSERT INTO call_logs (call_id, phone_number, start_time, duration) VALUES (?, ?, ?, ?)',
                       (call_id, target_number, start_time, duration))
        conn.commit()
        conn.close()
        
        message = f'Call initiated to {target_number} (Call ID: {call_id})'
        if forwarding:
            message += f' (forwarded from {phone_number})'
        return jsonify({'message': message}), 200
    except Exception as e:
        logging.error(f'Error in /api/call: {str(e)}')
        return jsonify({'error': f'Failed to initiate call: {str(e)}'}), 500

@app.route('/api/call_logs', methods=['GET'])
def get_call_logs():
    if not check_api_key():
        return jsonify({'error': 'Unauthorized'}), 401
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('SELECT call_id, phone_number, start_time, duration FROM call_logs')
        logs = [{'call_id': row[0], 'phone_number': row[1], 'start_time': row[2], 'duration': row[3]} for row in cursor.fetchall()]
        conn.close()
        return jsonify(logs), 200
    except Exception as e:
        logging.error(f'Error in /api/call_logs: {str(e)}')
        return jsonify({'error': f'Failed to fetch call logs: {str(e)}'}), 500

@app.route('/api/settings', methods=['POST'])
def update_settings():
    if not check_api_key():
        return jsonify({'error': 'Unauthorized'}), 401
    try:
        data = request.get_json()
        call_forwarding = data.get('call_forwarding', '')
        if call_forwarding and not validate_phone(call_forwarding):
            return jsonify({'error': 'Valid call forwarding number required'}), 400
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('UPDATE settings SET value = ? WHERE key = ?', (call_forwarding, 'call_forwarding'))
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'Settings updated successfully'}), 200
    except Exception as e:
        logging.error(f'Error in /api/settings POST: {str(e)}')
        return jsonify({'error': f'Failed to update settings: {str(e)}'}), 500

@app.route('/api/settings', methods=['GET'])
def get_settings():
    if not check_api_key():
        return jsonify({'error': 'Unauthorized'}), 401
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        cursor.execute('SELECT value FROM settings WHERE key = ?', ('call_forwarding',))
        result = cursor.fetchone()
        conn.close()
        return jsonify({'call_forwarding': result[0] if result else ''}), 200
    except Exception as e:
        logging.error(f'Error in /api/settings GET: {str(e)}')
        return jsonify({'error': f'Failed to fetch settings: {str(e)}'}), 500

@app.route('/static/styles.css')
def serve_styles():
    return send_file('styles.css')

@app.route('/')
def serve_index():
    with open('index.html', 'r') as f:
        return f.read()

if __name__ == '__main__':
    app.run(debug=False, host=HOST, port=PORT)