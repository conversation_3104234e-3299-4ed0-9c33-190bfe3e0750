<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; script-src 'self'">
  <title>VoIP Cloud Dashboard</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

      --bg-primary: #0f0f23;
      --bg-secondary: #1a1a2e;
      --bg-card: rgba(255, 255, 255, 0.05);
      --bg-card-hover: rgba(255, 255, 255, 0.1);

      --text-primary: #ffffff;
      --text-secondary: #a0a0a0;
      --text-muted: #666666;

      --border-color: rgba(255, 255, 255, 0.1);
      --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
      --shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.2);
      --shadow-heavy: 0 24px 96px rgba(0, 0, 0, 0.3);

      --border-radius: 16px;
      --border-radius-lg: 24px;
      --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: var(--bg-primary);
      background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
      min-height: 100vh;
      color: var(--text-primary);
      overflow-x: hidden;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      position: relative;
    }

    /* Header */
    .header {
      text-align: center;
      margin-bottom: 3rem;
      position: relative;
    }

    .title {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 700;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 0.5rem;
      letter-spacing: -0.02em;
    }

    .subtitle-text {
      font-size: 1.2rem;
      color: var(--text-secondary);
      font-weight: 300;
    }

    /* Theme Toggle */
    .theme-toggle {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: var(--bg-card);
      border: 1px solid var(--border-color);
      border-radius: 50px;
      padding: 0.5rem;
      cursor: pointer;
      transition: var(--transition);
      backdrop-filter: blur(10px);
    }

    .theme-toggle:hover {
      background: var(--bg-card-hover);
      transform: scale(1.05);
    }

    .theme-toggle i {
      font-size: 1.2rem;
      color: var(--text-primary);
    }

    /* Glass Card Effect */
    .glass-card {
      background: var(--bg-card);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-medium);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }

    .glass-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    }

    .glass-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-heavy);
      background: var(--bg-card-hover);
    }

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
      gap: 0.75rem;
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
      background: var(--primary-gradient);
      box-shadow: var(--shadow-light);
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    /* Modern Input Styling */
    .input-group {
      position: relative;
      margin-bottom: 1.5rem;
    }

    .input-label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--text-secondary);
      margin-bottom: 0.5rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .input {
      width: 100%;
      padding: 1rem 1rem 1rem 3rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 1rem;
      color: var(--text-primary);
      transition: var(--transition);
      backdrop-filter: blur(10px);
    }

    .input::placeholder {
      color: var(--text-muted);
    }

    .input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      background: rgba(255, 255, 255, 0.08);
    }

    .input-icon {
      position: absolute;
      left: 1rem;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-muted);
      font-size: 1.1rem;
      pointer-events: none;
    }

    /* Modern Button Styling */
    .btn {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 600;
      border: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      transition: var(--transition);
      text-decoration: none;
      overflow: hidden;
      min-width: 140px;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn-primary {
      background: var(--primary-gradient);
      color: white;
      box-shadow: var(--shadow-light);
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }

    .btn-success {
      background: var(--success-gradient);
      color: white;
      box-shadow: var(--shadow-light);
    }

    .btn-success:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }

    .btn-warning {
      background: var(--warning-gradient);
      color: var(--bg-primary);
      box-shadow: var(--shadow-light);
    }

    .btn-warning:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }

    .btn i {
      font-size: 1.1rem;
    }

    /* Loading Animation */
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255,255,255,0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* Status Messages */
    .status-message {
      margin-top: 1rem;
      padding: 1rem;
      border-radius: var(--border-radius);
      font-size: 0.9rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      backdrop-filter: blur(10px);
      border: 1px solid transparent;
      transition: var(--transition);
    }

    .status-success {
      background: rgba(67, 233, 123, 0.1);
      border-color: rgba(67, 233, 123, 0.3);
      color: #43e97b;
    }

    .status-error {
      background: rgba(250, 112, 154, 0.1);
      border-color: rgba(250, 112, 154, 0.3);
      color: #fa709a;
    }

    /* Modern Table */
    .table-container {
      margin-top: 1.5rem;
      border-radius: var(--border-radius);
      overflow: hidden;
      background: var(--bg-card);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-color);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th {
      background: rgba(102, 126, 234, 0.1);
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.875rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border-bottom: 1px solid var(--border-color);
    }

    td {
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    tbody tr {
      transition: var(--transition);
    }

    tbody tr:hover {
      background: rgba(255, 255, 255, 0.02);
    }

    tbody tr:last-child td {
      border-bottom: none;
    }

    /* Grid Layout */
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .glass-card {
        padding: 1.5rem;
      }

      .title {
        font-size: 2.5rem;
      }
    }

    /* Light Theme */
    [data-theme="light"] {
      --bg-primary: #f8fafc;
      --bg-secondary: #ffffff;
      --bg-card: rgba(255, 255, 255, 0.8);
      --bg-card-hover: rgba(255, 255, 255, 0.95);
      --text-primary: #1a202c;
      --text-secondary: #4a5568;
      --text-muted: #a0aec0;
      --border-color: rgba(0, 0, 0, 0.1);
      --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.08);
      --shadow-medium: 0 16px 64px rgba(0, 0, 0, 0.12);
      --shadow-heavy: 0 24px 96px rgba(0, 0, 0, 0.16);
    }

    [data-theme="light"] body {
      background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.08) 0%, transparent 50%);
    }

    [data-theme="light"] .input {
      background: rgba(0, 0, 0, 0.02);
    }

    [data-theme="light"] .input:focus {
      background: rgba(0, 0, 0, 0.04);
    }

    /* Floating Action Elements */
    .floating-stats {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      background: var(--bg-card);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      padding: 1rem;
      box-shadow: var(--shadow-medium);
      z-index: 1000;
      transition: var(--transition);
    }

    .floating-stats:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-heavy);
    }

    .stats-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .stats-item:last-child {
      margin-bottom: 0;
    }

    .stats-value {
      font-weight: 600;
      color: var(--text-primary);
    }
  </style>
</head>
<body data-theme="dark">
  <!-- Theme Toggle -->
  <div class="theme-toggle" onclick="toggleTheme()">
    <i class="fas fa-moon" id="theme-icon"></i>
  </div>

  <div class="container">
    <!-- Header -->
    <div class="header">
      <h1 class="title">VoIP Cloud Dashboard</h1>
      <p class="subtitle-text">Manage your calls with modern cloud technology</p>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
      <!-- Initiate Call Card -->
      <div class="glass-card">
        <div class="card-header">
          <div class="card-icon">
            <i class="fas fa-phone"></i>
          </div>
          <h2 class="card-title">Initiate Call</h2>
        </div>

        <div class="input-group">
          <label class="input-label">Phone Number</label>
          <div style="position: relative;">
            <i class="fas fa-phone input-icon"></i>
            <input type="text" id="phone-number" class="input" placeholder="+****************">
          </div>
        </div>

        <button id="start-call" class="btn btn-primary">
          <i class="fas fa-phone-alt"></i>
          <span>Start Call</span>
        </button>
        <div id="call-feedback" class="status-message status-success" style="display: none;"></div>
      </div>

      <!-- Settings Card -->
      <div class="glass-card">
        <div class="card-header">
          <div class="card-icon" style="background: var(--success-gradient);">
            <i class="fas fa-cog"></i>
          </div>
          <h2 class="card-title">Settings</h2>
        </div>

        <div class="input-group">
          <label class="input-label">Call Forwarding Number</label>
          <div style="position: relative;">
            <i class="fas fa-share input-icon"></i>
            <input type="text" id="call-forwarding" class="input" placeholder="+****************">
          </div>
        </div>

        <button id="save-settings" class="btn btn-success">
          <i class="fas fa-save"></i>
          <span>Save Settings</span>
        </button>
        <div id="settings-feedback" class="status-message status-success" style="display: none;"></div>
      </div>
    </div>

    <!-- Call Logs Card -->
    <div class="glass-card">
      <div class="card-header">
        <div class="card-icon" style="background: var(--warning-gradient);">
          <i class="fas fa-history"></i>
        </div>
        <h2 class="card-title">Call History</h2>
        <div style="margin-left: auto;">
          <button id="refresh-logs" class="btn btn-warning">
            <i class="fas fa-sync-alt"></i>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      <div class="table-container">
        <table id="call-logs">
          <thead>
            <tr>
              <th><i class="fas fa-fingerprint"></i> Call ID</th>
              <th><i class="fas fa-phone"></i> Phone Number</th>
              <th><i class="fas fa-clock"></i> Start Time</th>
              <th><i class="fas fa-stopwatch"></i> Duration</th>
            </tr>
          </thead>
          <tbody id="call-logs-body">
            <!-- Dynamic content will be loaded here -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Floating Stats -->
  <div class="floating-stats">
    <div class="stats-item">
      <i class="fas fa-phone-volume"></i>
      <span>Total Calls: <span class="stats-value" id="total-calls">0</span></span>
    </div>
    <div class="stats-item">
      <i class="fas fa-clock"></i>
      <span>Avg Duration: <span class="stats-value" id="avg-duration">0s</span></span>
    </div>
  </div>

  <script>
    // Theme Management
    function toggleTheme() {
      const body = document.body;
      const themeIcon = document.getElementById('theme-icon');
      const currentTheme = body.getAttribute('data-theme');

      if (currentTheme === 'dark') {
        body.setAttribute('data-theme', 'light');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'light');
      } else {
        body.setAttribute('data-theme', 'dark');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'dark');
      }
    }

    // Load saved theme
    function loadTheme() {
      const savedTheme = localStorage.getItem('theme') || 'dark';
      const body = document.body;
      const themeIcon = document.getElementById('theme-icon');

      body.setAttribute('data-theme', savedTheme);
      themeIcon.className = savedTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
    }

    // Enhanced API Key Management
    let apiKey = localStorage.getItem('voip-api-key') || prompt('Enter API Key (default: secret-voip-key):') || 'secret-voip-key';
    localStorage.setItem('voip-api-key', apiKey);

    function validatePhone(phone) {
      const pattern = /^\\+?\\d{10,15}$/;
      return pattern.test(phone);
    }

    function showMessage(elementId, message, type = 'success') {
      const element = document.getElementById(elementId);
      element.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}`;
      element.className = `status-message status-${type === 'success' ? 'success' : 'error'}`;
      element.style.display = 'flex';

      setTimeout(() => {
        element.style.display = 'none';
      }, 5000);
    }

    function setButtonLoading(buttonId, loading = true) {
      const button = document.getElementById(buttonId);
      const span = button.querySelector('span');
      const icon = button.querySelector('i');

      if (loading) {
        button.disabled = true;
        icon.className = 'loading';
        span.textContent = 'Processing...';
      } else {
        button.disabled = false;
        // Reset based on button type
        if (buttonId === 'start-call') {
          icon.className = 'fas fa-phone-alt';
          span.textContent = 'Start Call';
        } else if (buttonId === 'save-settings') {
          icon.className = 'fas fa-save';
          span.textContent = 'Save Settings';
        } else if (buttonId === 'refresh-logs') {
          icon.className = 'fas fa-sync-alt';
          span.textContent = 'Refresh';
        }
      }
    }

    async function fetchCallLogs() {
      setButtonLoading('refresh-logs', true);
      try {
        const response = await fetch('/api/call_logs?limit=20', {
          headers: { 'X-API-Key': apiKey }
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):') || 'secret-voip-key';
            localStorage.setItem('voip-api-key', apiKey);
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        const data = await response.json();
        const logs = data.logs || data; // Handle both new and old API format
        const tbody = document.getElementById('call-logs-body');
        tbody.innerHTML = '';

        if (logs.length === 0) {
          tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: var(--text-muted); padding: 2rem;">No calls found</td></tr>';
        } else {
          logs.forEach(log => {
            const row = document.createElement('tr');
            const startTime = new Date(log.start_time).toLocaleString();
            const duration = log.duration || 0;
            const status = log.status || 'unknown';
            const cost = log.cost ? `$${log.cost.toFixed(2)}` : '-';

            // Status indicator
            let statusIcon = '';
            let statusColor = '';
            switch(status) {
              case 'completed':
                statusIcon = '✅';
                statusColor = 'var(--text-primary)';
                break;
              case 'active':
                statusIcon = '🔄';
                statusColor = '#43e97b';
                break;
              case 'failed':
                statusIcon = '❌';
                statusColor = '#fa709a';
                break;
              default:
                statusIcon = '⏳';
                statusColor = 'var(--text-muted)';
            }

            row.innerHTML = `
              <td><code style="font-size: 0.8rem; color: var(--text-muted);">${log.call_id.substring(0, 8)}...</code></td>
              <td>
                <strong>${log.destination_number || log.phone_number}</strong>
                ${log.destination_number && log.phone_number !== log.destination_number ?
                  `<br><small style="color: var(--text-muted);">from ${log.phone_number}</small>` : ''}
              </td>
              <td>${startTime}</td>
              <td>
                <span style="color: ${statusColor};">${statusIcon} ${duration}s</span>
                ${cost !== '-' ? `<br><small style="color: var(--text-muted);">${cost}</small>` : ''}
              </td>
            `;
            tbody.appendChild(row);
          });
        }

        // Update stats
        await updateStats();
      } catch (error) {
        showMessage('call-feedback', 'Error: ' + error.message, 'error');
      } finally {
        setButtonLoading('refresh-logs', false);
      }
    }

    async function updateStats() {
      try {
        const response = await fetch('/api/stats?days=7', {
          headers: { 'X-API-Key': apiKey }
        });
        if (response.ok) {
          const stats = await response.json();
          document.getElementById('total-calls').textContent = stats.totals.total_calls;
          document.getElementById('avg-duration').textContent = stats.totals.avg_duration + 's';
        }
      } catch (error) {
        console.error('Failed to update stats:', error);
        // Fallback to basic stats from call logs
        const response = await fetch('/api/call_logs?limit=100', {
          headers: { 'X-API-Key': apiKey }
        });
        if (response.ok) {
          const data = await response.json();
          const logs = data.logs || data;
          const totalCalls = logs.length;
          const completedLogs = logs.filter(log => log.duration > 0);
          const avgDuration = completedLogs.length > 0 ?
            Math.round(completedLogs.reduce((sum, log) => sum + log.duration, 0) / completedLogs.length) : 0;

          document.getElementById('total-calls').textContent = totalCalls;
          document.getElementById('avg-duration').textContent = avgDuration + 's';
        }
      }
    }

    async function fetchActiveCallsCount() {
      try {
        const response = await fetch('/api/active_calls', {
          headers: { 'X-API-Key': apiKey }
        });
        if (response.ok) {
          const data = await response.json();
          const activeCount = data.count || 0;

          // Update UI to show active calls
          const statsContainer = document.querySelector('.floating-stats');
          let activeCallsElement = document.getElementById('active-calls-stat');

          if (!activeCallsElement) {
            activeCallsElement = document.createElement('div');
            activeCallsElement.id = 'active-calls-stat';
            activeCallsElement.className = 'stats-item';
            statsContainer.insertBefore(activeCallsElement, statsContainer.firstChild);
          }

          activeCallsElement.innerHTML = `
            <i class="fas fa-phone-volume"></i>
            <span>Active Calls: <span class="stats-value">${activeCount}</span></span>
          `;

          if (activeCount > 0) {
            activeCallsElement.style.color = '#43e97b';
          } else {
            activeCallsElement.style.color = 'var(--text-secondary)';
          }
        }
      } catch (error) {
        console.error('Failed to fetch active calls:', error);
      }
    }

    // Enhanced Event Listeners
    document.getElementById('start-call').addEventListener('click', async () => {
      const phoneNumber = document.getElementById('phone-number').value.trim();
      if (!validatePhone(phoneNumber)) {
        showMessage('call-feedback', 'Please enter a valid phone number (e.g., +1234567890)', 'error');
        return;
      }

      setButtonLoading('start-call', true);
      try {
        const response = await fetch('/api/call', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-API-Key': apiKey },
          body: JSON.stringify({ phone_number: phoneNumber })
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):') || 'secret-voip-key';
            localStorage.setItem('voip-api-key', apiKey);
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        const data = await response.json();
        showMessage('call-feedback', data.message, 'success');
        document.getElementById('phone-number').value = ''; // Clear input
        fetchCallLogs(); // Refresh logs after call
      } catch (error) {
        showMessage('call-feedback', 'Error: ' + error.message, 'error');
      } finally {
        setButtonLoading('start-call', false);
      }
    });

    document.getElementById('refresh-logs').addEventListener('click', fetchCallLogs);

    document.getElementById('save-settings').addEventListener('click', async () => {
      const callForwarding = document.getElementById('call-forwarding').value.trim();
      if (callForwarding && !validatePhone(callForwarding)) {
        showMessage('settings-feedback', 'Please enter a valid call forwarding number', 'error');
        return;
      }

      setButtonLoading('save-settings', true);
      try {
        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-API-Key': apiKey },
          body: JSON.stringify({ call_forwarding: callForwarding })
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):') || 'secret-voip-key';
            localStorage.setItem('voip-api-key', apiKey);
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP ${response.status}: ${await response.text()}`);
        }
        const data = await response.json();
        showMessage('settings-feedback', data.message, 'success');
      } catch (error) {
        showMessage('settings-feedback', 'Error: ' + error.message, 'error');
      } finally {
        setButtonLoading('save-settings', false);
      }
    });

    // Enhanced phone number formatting
    document.getElementById('phone-number').addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length > 0) {
        if (value.length <= 3) {
          value = `+1 (${value}`;
        } else if (value.length <= 6) {
          value = `+1 (${value.slice(0, 3)}) ${value.slice(3)}`;
        } else {
          value = `+1 (${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
        }
      }
      e.target.value = value;
    });

    document.getElementById('call-forwarding').addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length > 0) {
        if (value.length <= 3) {
          value = `+1 (${value}`;
        } else if (value.length <= 6) {
          value = `+1 (${value.slice(0, 3)}) ${value.slice(3)}`;
        } else {
          value = `+1 (${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
        }
      }
      e.target.value = value;
    });

    // Auto-refresh functionality
    let refreshInterval;

    function startAutoRefresh() {
      // Refresh call logs every 30 seconds
      refreshInterval = setInterval(() => {
        fetchCallLogs();
        fetchActiveCallsCount();
      }, 30000);
    }

    function stopAutoRefresh() {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
      }
    }

    // Initialize app
    window.addEventListener('DOMContentLoaded', async () => {
      loadTheme();

      // Initial data load
      await fetchCallLogs();
      await fetchActiveCallsCount();

      // Load settings
      try {
        const response = await fetch('/api/settings', { headers: { 'X-API-Key': apiKey } });
        if (response.ok) {
          const data = await response.json();
          if (data.call_forwarding && data.call_forwarding.value) {
            document.getElementById('call-forwarding').value = data.call_forwarding.value;
          }
        }
      } catch (error) {
        console.error('Settings load error:', error);
      }

      // Start auto-refresh
      startAutoRefresh();

      // Add visibility change handler to pause/resume auto-refresh
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          stopAutoRefresh();
        } else {
          startAutoRefresh();
          fetchCallLogs(); // Immediate refresh when tab becomes visible
          fetchActiveCallsCount();
        }
      });

      // Add keyboard shortcuts
      document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
          switch(e.key) {
            case 'r':
              e.preventDefault();
              fetchCallLogs();
              break;
            case 'n':
              e.preventDefault();
              document.getElementById('phone-number').focus();
              break;
          }
        }
      });

      console.log('🚀 VoIP Dashboard initialized successfully');
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      stopAutoRefresh();
    });
  </script>
</body>
</html>
