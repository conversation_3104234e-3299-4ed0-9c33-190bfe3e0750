<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self'">
  <title>Cloud VoIP Dashboard</title>
  <style>
    /* Global Styles */
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      margin: 0;
      padding: 0;
      color: #333;
    }

    .container {
      max-width: 900px;
      margin: 40px auto;
      padding: 20px;
    }

    .title {
      text-align: center;
      font-size: 2.2rem;
      font-weight: 700;
      margin-bottom: 30px;
      color: #2d3748;
    }

    /* Card Layout */
    .card {
      background: #fff;
      border-radius: 12px;
      padding: 20px 25px;
      margin-bottom: 25px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.2s ease;
    }

    .card:hover {
      transform: translateY(-3px);
    }

    .subtitle {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #1a202c;
    }

    /* Inputs */
    .input {
      padding: 10px 14px;
      width: 100%;
      margin-top: 6px;
      border: 1px solid #cbd5e0;
      border-radius: 8px;
      font-size: 0.95rem;
      transition: border 0.2s;
    }

    .input:focus {
      outline: none;
      border-color: #3182ce;
      box-shadow: 0 0 0 2px rgba(49,130,206,0.2);
    }

    /* Buttons */
    .btn {
      display: inline-block;
      margin-top: 12px;
      padding: 10px 18px;
      font-size: 0.95rem;
      font-weight: 600;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn.blue {
      background: #3182ce;
      color: #fff;
    }
    .btn.blue:hover {
      background: #2b6cb0;
    }

    .btn.green {
      background: #38a169;
      color: #fff;
    }
    .btn.green:hover {
      background: #2f855a;
    }

    .btn.yellow {
      background: #ecc94b;
      color: #1a202c;
    }
    .btn.yellow:hover {
      background: #d69e2e;
    }

    /* Feedback Messages */
    .output {
      margin-top: 10px;
      font-size: 0.9rem;
      padding: 8px 10px;
      border-radius: 6px;
    }
    .output.green {
      background: #f0fff4;
      color: #276749;
    }
    .output.red {
      background: #fff5f5;
      color: #c53030;
    }

    /* Table Styling */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      border-radius: 8px;
      overflow: hidden;
    }

    th, td {
      padding: 12px 14px;
      text-align: left;
      font-size: 0.95rem;
    }

    thead {
      background: #3182ce;
      color: #fff;
    }

    tbody tr:nth-child(odd) {
      background: #f7fafc;
    }

    tbody tr:hover {
      background: #ebf8ff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">Cloud VoIP Dashboard</h1>

    <!-- Initiate Call -->
    <div class="card">
      <h2 class="subtitle">Initiate Call</h2>
      <label>
        Phone Number:
        <input type="text" id="phone-number" class="input" placeholder="+1234567890">
      </label>
      <button id="start-call" class="btn blue">Start Call</button>
      <div id="call-feedback" class="output green"></div>
    </div>

    <!-- Call Logs -->
    <div class="card">
      <h2 class="subtitle">Call Logs</h2>
      <table id="call-logs">
        <thead>
          <tr>
            <th>Call ID</th>
            <th>Phone Number</th>
            <th>Start Time</th>
            <th>Duration (s)</th>
          </tr>
        </thead>
        <tbody id="call-logs-body"></tbody>
      </table>
      <button id="refresh-logs" class="btn yellow">Refresh Logs</button>
    </div>

    <!-- Settings -->
    <div class="card">
      <h2 class="subtitle">Settings</h2>
      <label>
        Call Forwarding Number:
        <input type="text" id="call-forwarding" class="input" placeholder="+1234567890">
      </label>
      <button id="save-settings" class="btn green">Save Settings</button>
      <div id="settings-feedback" class="output green"></div>
    </div>
  </div>

  <!-- Existing JavaScript (unchanged functionality) -->
  <script>
    let apiKey = prompt('Enter API Key (default: secret-voip-key):');

    function validatePhone(phone) {
      const pattern = /^\\+?\\d{10,15}$/;
      return pattern.test(phone);
    }

    async function fetchCallLogs() {
      try {
        const response = await fetch('/api/call_logs', {
          headers: { 'X-API-Key': apiKey }
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):');
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP \${response.status}: \${await response.text()}`);
        }
        const logs = await response.json();
        const tbody = document.getElementById('call-logs-body');
        tbody.innerHTML = '';
        logs.forEach(log => {
          const row = document.createElement('tr');
          row.innerHTML =`
            <td>\${log.call_id}</td>
            <td>\${log.phone_number}</td>
            <td>\${log.start_time}</td>
            <td>\${log.duration}</td>
          `;
          tbody.appendChild(row);
        });
      } catch (error) {
        document.getElementById('call-feedback').innerText = 'Error: ' + error.message;
      }
    }

    document.getElementById('start-call').addEventListener('click', async () => {
      const phoneNumber = document.getElementById('phone-number').value;
      if (!validatePhone(phoneNumber)) {
        document.getElementById('call-feedback').innerText = 'Please enter a valid phone number (e.g., +1234567890)';
        return;
      }
      try {
        const response = await fetch('/api/call', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-API-Key': apiKey },
          body: JSON.stringify({ phone_number: phoneNumber })
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):');
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP \${response.status}: \${await response.text()}`);
        }
        const data = await response.json();
        document.getElementById('call-feedback').innerText = data.message;
        fetchCallLogs(); // Refresh logs after call
      } catch (error) {
        document.getElementById('call-feedback').innerText = 'Error: ' + error.message;
      }
    });

    document.getElementById('refresh-logs').addEventListener('click', fetchCallLogs);

    document.getElementById('save-settings').addEventListener('click', async () => {
      const callForwarding = document.getElementById('call-forwarding').value;
      if (callForwarding && !validatePhone(callForwarding)) {
        document.getElementById('settings-feedback').innerText = 'Please enter a valid call forwarding number';
        return;
      }
      try {
        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'X-API-Key': apiKey },
          body: JSON.stringify({ call_forwarding: callForwarding })
        });
        if (!response.ok) {
          if (response.status === 401) {
            apiKey = prompt('Invalid API Key. Enter API Key (default: secret-voip-key):');
            throw new Error('Unauthorized. Try again with new key.');
          }
          throw new Error(`HTTP \${response.status}: \${await response.text()}`);
        }
        const data = await response.json();
        document.getElementById('settings-feedback').innerText = data.message;
      } catch (error) {
        document.getElementById('settings-feedback').innerText = 'Error: ' + error.message;
      }
    });

    // Load call logs and settings on page load
    window.onload = () => {
      fetchCallLogs();
      fetch('/api/settings', { headers: { 'X-API-Key': apiKey } })
        .then(response => {
          if (!response.ok) throw new Error('Failed to load settings');
          return response.json();
        })
        .then(data => {
          document.getElementById('call-forwarding').value = data.call_forwarding;
        })
        .catch(error => {
          document.getElementById('settings-feedback').innerText = 'Error: ' + error.message;
        });
    };
  </script>
</body>
</html>
