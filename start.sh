#!/bin/bash

# VoIP Dashboard Startup Script

echo "🚀 Starting VoIP Dashboard..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️  Creating .env file from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration"
fi

# Start the application
echo "🌟 Starting VoIP Dashboard Server..."
echo "📱 Access the dashboard at: http://localhost:5000"
echo "🔑 Default API Key: secret-voip-key"
echo "👤 Default Admin: admin / admin123"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

python3 app.py
