/* Modern VoIP Dashboard Styles */
/* This file provides additional styling that complements the main styles in index.html */

/* Enhanced animations for external use */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Additional utility classes */
.animate-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-slide-in {
  animation: slideInRight 0.4s ease-out;
}

/* Enhanced focus states for accessibility */
.btn:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.input:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Print styles */
@media print {
  .theme-toggle,
  .floating-stats {
    display: none;
  }

  .glass-card {
    background: white;
    border: 1px solid #ccc;
    box-shadow: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: rgba(255, 255, 255, 0.3);
    --bg-card: rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}