body {
  background-color: #f3f4f6;
  margin: 0;
  font-family: Arial, sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
  padding: 1rem;
}

.title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  text-align: center;
  margin: 1rem 0;
}

.card {
  background-color: white;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
}

.input {
  display: block;
  padding: 0.5rem;
  width: 100%;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  margin-top: 0.25rem;
}

.btn {
  padding: 0.5rem;
  width: 100%;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background-color 0.3s;
}

.btn:hover {
  filter: brightness(90%);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.blue { background-color: #3b82f6; }
.btn.yellow { background-color: #f59e0b; }
.btn.green { background-color: #10b981; }

.output.green {
  color: #059669;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.output.error {
  color: #ef4444;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

th, td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

th {
  background-color: #f9fafb;
  font-weight: 600;
}

@media (max-width: 600px) {
  .card {
    max-width: 100%;
  }
  .btn {
    font-size: 0.9rem;
  }
}