# VoIP Dashboard Configuration
# Copy this file to .env and update the values

# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_ENV=production

# Security
VOIP_API_KEY=your-secure-api-key-here

# VoIP Provider Settings (for production integration)
VOIP_PROVIDER_URL=https://api.your-voip-provider.com
VOIP_PROVIDER_KEY=your-provider-api-key
VOIP_PROVIDER_SECRET=your-provider-secret

# Database
DB_FILE=voip.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=voip.log
