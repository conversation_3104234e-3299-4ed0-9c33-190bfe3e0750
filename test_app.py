#!/usr/bin/env python3
"""
Test script for VoIP Dashboard
"""

import sys
import os
import sqlite3
import requests
import time
import json

def test_database():
    """Test database initialization"""
    print("🔍 Testing database initialization...")
    try:
        # Import and initialize database
        sys.path.append(os.path.dirname(__file__))
        from app import init_db, DB_FILE
        
        # Check if database exists and has correct tables
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['call_logs', 'settings', 'users', 'call_stats']
        for table in expected_tables:
            if table in tables:
                print(f"  ✅ Table '{table}' exists")
            else:
                print(f"  ❌ Table '{table}' missing")
                return False
        
        # Check settings
        cursor.execute("SELECT COUNT(*) FROM settings")
        settings_count = cursor.fetchone()[0]
        print(f"  ✅ Settings table has {settings_count} entries")
        
        conn.close()
        print("✅ Database test passed")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    try:
        import flask
        print(f"  ✅ Flask {flask.__version__}")
        
        import flask_limiter
        print("  ✅ Flask-Limiter")
        
        import flask_cors
        print("  ✅ Flask-CORS")
        
        import werkzeug
        print(f"  ✅ Werkzeug {werkzeug.__version__}")
        
        import requests
        print(f"  ✅ Requests {requests.__version__}")
        
        print("✅ All imports successful")
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_app_startup():
    """Test that the app can start without errors"""
    print("🔍 Testing app startup...")
    try:
        # Import app components
        from app import app, validate_phone, normalize_phone
        
        # Test utility functions
        assert validate_phone("+1234567890") == True
        assert validate_phone("invalid") == False
        assert normalize_phone("1234567890") == "+11234567890"
        
        print("  ✅ Utility functions work")
        
        # Test app configuration
        assert app.config is not None
        print("  ✅ Flask app configured")
        
        print("✅ App startup test passed")
        return True
        
    except Exception as e:
        print(f"❌ App startup test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints (requires running server)"""
    print("🔍 Testing API endpoints...")
    base_url = "http://localhost:5000"
    headers = {"X-API-Key": "secret-voip-key"}
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("  ✅ Health endpoint working")
        else:
            print(f"  ⚠️  Health endpoint returned {response.status_code}")
        
        # Test call logs endpoint
        response = requests.get(f"{base_url}/api/call_logs", headers=headers, timeout=5)
        if response.status_code == 200:
            print("  ✅ Call logs endpoint working")
        else:
            print(f"  ⚠️  Call logs endpoint returned {response.status_code}")
        
        # Test settings endpoint
        response = requests.get(f"{base_url}/api/settings", headers=headers, timeout=5)
        if response.status_code == 200:
            print("  ✅ Settings endpoint working")
        else:
            print(f"  ⚠️  Settings endpoint returned {response.status_code}")
        
        print("✅ API endpoints test passed")
        return True
        
    except requests.exceptions.ConnectionError:
        print("  ⚠️  Server not running - skipping API tests")
        print("  💡 Run 'python3 app.py' in another terminal to test APIs")
        return True
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 VoIP Dashboard Test Suite")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_database,
        test_app_startup,
        test_api_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Your VoIP Dashboard is ready to use.")
        print("\n🚀 To start the server:")
        print("   python3 app.py")
        print("\n🌐 Then visit: http://localhost:5000")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
