#!/usr/bin/env python3
"""
VoIP Dashboard Demo Script
Demonstrates the functionality without running the full server
"""

import sys
import os
import sqlite3
import json
from datetime import datetime, timedelta
import uuid
import random

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

def create_demo_data():
    """Create some demo call data"""
    print("🎭 Creating demo data...")
    
    try:
        from app import init_db, DB_FILE, calculate_call_cost, normalize_phone
        
        # Initialize database
        init_db()
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Clear existing demo data
        cursor.execute("DELETE FROM call_logs WHERE phone_number LIKE '+1555%'")
        
        # Create demo calls
        demo_calls = [
            ("+15551234567", "completed", 120),
            ("+15559876543", "completed", 45),
            ("+15555551234", "failed", 0),
            ("+15551112222", "completed", 300),
            ("+15553334444", "completed", 180),
            ("+15557778888", "active", 60),
        ]
        
        for i, (phone, status, duration) in enumerate(demo_calls):
            call_id = str(uuid.uuid4())
            start_time = datetime.now() - timedelta(hours=random.randint(1, 24))
            end_time = start_time + timedelta(seconds=duration) if status == "completed" else None
            cost = calculate_call_cost(duration) if status == "completed" else 0.0
            
            cursor.execute('''INSERT INTO call_logs 
                             (call_id, phone_number, destination_number, start_time, end_time, 
                              duration, status, call_type, cost) 
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                          (call_id, phone, phone, start_time.isoformat(), 
                           end_time.isoformat() if end_time else None,
                           duration, status, 'outbound', cost))
        
        # Update settings
        cursor.execute('''UPDATE settings SET value = ? WHERE key = ?''', 
                      ('+15551111111', 'call_forwarding'))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Created {len(demo_calls)} demo calls")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create demo data: {e}")
        return False

def show_demo_stats():
    """Show demo statistics"""
    print("📊 Demo Statistics:")
    
    try:
        from app import DB_FILE
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Get call counts by status
        cursor.execute('''SELECT status, COUNT(*), AVG(duration), SUM(cost)
                         FROM call_logs 
                         GROUP BY status''')
        
        stats = cursor.fetchall()
        
        print("\n📞 Call Summary:")
        total_calls = 0
        total_cost = 0
        
        for status, count, avg_duration, cost in stats:
            total_calls += count
            total_cost += cost or 0
            print(f"  {status.upper()}: {count} calls, avg {avg_duration or 0:.1f}s, ${cost or 0:.2f}")
        
        print(f"\n💰 Total: {total_calls} calls, ${total_cost:.2f}")
        
        # Get recent calls
        cursor.execute('''SELECT phone_number, status, duration, cost, start_time
                         FROM call_logs 
                         ORDER BY start_time DESC 
                         LIMIT 5''')
        
        recent_calls = cursor.fetchall()
        
        print("\n📋 Recent Calls:")
        for phone, status, duration, cost, start_time in recent_calls:
            time_str = datetime.fromisoformat(start_time).strftime("%H:%M")
            print(f"  {time_str} - {phone} ({status}) {duration}s ${cost or 0:.2f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to show stats: {e}")
        return False

def test_voip_functions():
    """Test VoIP utility functions"""
    print("🔧 Testing VoIP Functions:")
    
    try:
        from app import validate_phone, normalize_phone, calculate_call_cost
        
        # Test phone validation
        test_phones = [
            ("+1234567890", True),
            ("1234567890", True),
            ("+15551234567", True),
            ("invalid", False),
            ("123", False),
        ]
        
        print("\n📱 Phone Validation:")
        for phone, expected in test_phones:
            result = validate_phone(phone)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {phone} -> {result}")
        
        # Test phone normalization
        print("\n🔄 Phone Normalization:")
        test_normalize = [
            ("1234567890", "+11234567890"),
            ("+15551234567", "+15551234567"),
            ("15551234567", "+15551234567"),
        ]
        
        for phone, expected in test_normalize:
            result = normalize_phone(phone)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {phone} -> {result}")
        
        # Test cost calculation
        print("\n💰 Cost Calculation:")
        test_costs = [30, 60, 120, 300]
        for duration in test_costs:
            cost = calculate_call_cost(duration)
            print(f"  {duration}s -> ${cost:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Function test failed: {e}")
        return False

def show_ui_preview():
    """Show what the UI looks like"""
    print("🎨 UI Preview:")
    print("""
╭─────────────────────────────────────────────────────────────╮
│                    🌟 VoIP Cloud Dashboard                   │
│                 Manage your calls with style                │
╰─────────────────────────────────────────────────────────────╯

┌─────────────────────────┐  ┌─────────────────────────────────┐
│     📞 Initiate Call    │  │        ⚙️ Settings             │
│                         │  │                                 │
│ Phone Number:           │  │ Call Forwarding:                │
│ ┌─────────────────────┐ │  │ ┌─────────────────────────────┐ │
│ │ +****************   │ │  │ │ +****************         │ │
│ └─────────────────────┘ │  │ └─────────────────────────────┘ │
│                         │  │                                 │
│    [📞 Start Call]      │  │     [💾 Save Settings]         │
│                         │  │                                 │
└─────────────────────────┘  └─────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    📋 Call History                          │
│                                                             │
│ Call ID      │ Phone Number    │ Start Time  │ Duration     │
│──────────────┼─────────────────┼─────────────┼──────────────│
│ abc12345...  │ +15551234567    │ 14:30       │ ✅ 120s     │
│ def67890...  │ +15559876543    │ 13:45       │ ✅ 45s      │
│ ghi11111...  │ +15555551234    │ 12:30       │ ❌ 0s       │
│                                                             │
│                    [🔄 Refresh]                             │
└─────────────────────────────────────────────────────────────┘

                        ┌─────────────────┐
                        │   📊 Stats      │
                        │ Total: 6 calls  │
                        │ Avg: 120s       │
                        │ Active: 1       │
                        └─────────────────┘

Features:
• 🌙 Dark/Light theme toggle
• 📱 Mobile responsive design  
• ⚡ Real-time updates
• 🎨 Glassmorphism UI effects
• 📊 Live statistics
• 🔄 Auto-refresh every 30s
""")

def main():
    """Run the demo"""
    print("🚀 VoIP Dashboard Demo")
    print("=" * 50)
    
    # Test functions
    if not test_voip_functions():
        return 1
    
    print()
    
    # Create demo data
    if not create_demo_data():
        return 1
    
    print()
    
    # Show stats
    if not show_demo_stats():
        return 1
    
    print()
    
    # Show UI preview
    show_ui_preview()
    
    print("=" * 50)
    print("🎉 Demo completed successfully!")
    print("\n🚀 To start the full application:")
    print("   python3 app.py")
    print("\n🌐 Then visit: http://localhost:5000")
    print("🔑 API Key: secret-voip-key")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
