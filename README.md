# 🚀 Modern VoIP Cloud Dashboard

A beautiful, modern web-based VoIP dashboard with real-time call management, analytics, and a stunning glassmorphism UI design.

![VoIP Dashboard](https://img.shields.io/badge/VoIP-Dashboard-blue?style=for-the-badge)
![Python](https://img.shields.io/badge/Python-3.8+-green?style=for-the-badge)
![Flask](https://img.shields.io/badge/Flask-2.3+-red?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

## ✨ Features

### 🎨 Modern UI/UX
- **Glassmorphism Design** - Beautiful glass-like cards with backdrop blur effects
- **Dark/Light Theme** - Toggle between themes with persistent preferences
- **Responsive Layout** - Works perfectly on desktop, tablet, and mobile
- **Smooth Animations** - Micro-interactions and loading states
- **Professional Typography** - Using Inter font for modern appeal

### 📞 VoIP Functionality
- **Real-time Call Management** - Initiate, monitor, and end calls
- **Call Forwarding** - Automatic call routing configuration
- **Live Call Tracking** - Monitor active calls in real-time
- **Call History** - Detailed logs with filtering and pagination
- **Cost Tracking** - Automatic call cost calculation

### 📊 Analytics & Reporting
- **Real-time Statistics** - Live dashboard with key metrics
- **Daily/Weekly Reports** - Comprehensive call analytics
- **Success Rate Monitoring** - Track call completion rates
- **Cost Analysis** - Monitor spending and usage patterns

### 🔧 Technical Features
- **RESTful API** - Complete API for integration
- **Rate Limiting** - Built-in protection against abuse
- **Error Handling** - Comprehensive error management
- **Logging** - Detailed application logging
- **Database Management** - SQLite with automatic migrations

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. **Clone or download the project**
   ```bash
   cd voip-dashboard
   ```

2. **Run the startup script**
   ```bash
   ./start.sh
   ```
   
   Or manually:
   ```bash
   # Create virtual environment
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Start the application
   python3 app.py
   ```

3. **Access the dashboard**
   - Open your browser to: http://localhost:5000
   - Default API Key: `secret-voip-key`
   - Default Admin: `admin` / `admin123`

## 🎯 Usage

### Making Calls
1. Enter a phone number in the "Initiate Call" section
2. Click "Start Call" to begin the call
3. Monitor active calls in real-time
4. View call history and statistics

### Settings Management
- Configure call forwarding numbers
- Adjust system preferences
- Monitor usage statistics

### API Integration
The dashboard provides a complete REST API:

```bash
# Get call logs
curl -H "X-API-Key: secret-voip-key" http://localhost:5000/api/call_logs

# Initiate a call
curl -X POST -H "Content-Type: application/json" \
     -H "X-API-Key: secret-voip-key" \
     -d '{"phone_number": "+**********"}' \
     http://localhost:5000/api/call

# Get statistics
curl -H "X-API-Key: secret-voip-key" http://localhost:5000/api/stats
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# Security
VOIP_API_KEY=your-secure-api-key-here

# VoIP Provider (for production)
VOIP_PROVIDER_URL=https://api.your-provider.com
VOIP_PROVIDER_KEY=your-api-key
VOIP_PROVIDER_SECRET=your-secret
```

### Database
The application uses SQLite by default. The database is automatically created and initialized on first run.

## 📱 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/call` | Initiate a new call |
| GET | `/api/call_logs` | Get call history |
| POST | `/api/call/<id>/end` | End an active call |
| GET | `/api/active_calls` | Get currently active calls |
| GET | `/api/stats` | Get call statistics |
| GET/POST | `/api/settings` | Manage settings |
| GET | `/api/health` | Health check |

## 🎨 UI Features

### Theme System
- **Dark Mode**: Professional dark theme with purple gradients
- **Light Mode**: Clean light theme for daytime use
- **Auto-persistence**: Theme preference saved locally

### Interactive Elements
- **Hover Effects**: Smooth card elevations and button animations
- **Loading States**: Animated spinners and progress indicators
- **Status Messages**: Beautiful success/error notifications
- **Real-time Updates**: Live data refresh every 30 seconds

### Responsive Design
- **Mobile-first**: Optimized for mobile devices
- **Tablet Support**: Perfect layout for tablets
- **Desktop**: Full-featured desktop experience

## 🔒 Security

- **API Key Authentication**: Secure API access
- **Rate Limiting**: Protection against abuse
- **Input Validation**: Comprehensive data validation
- **Error Handling**: Secure error responses
- **CORS Support**: Configurable cross-origin requests

## 🚀 Production Deployment

### Docker (Recommended)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

### Traditional Deployment
1. Use a production WSGI server (Gunicorn, uWSGI)
2. Configure reverse proxy (Nginx, Apache)
3. Set up SSL certificates
4. Configure environment variables
5. Set up monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API endpoints

## 🎉 Acknowledgments

- Built with Flask and modern web technologies
- UI inspired by glassmorphism design trends
- Icons by Font Awesome
- Typography by Google Fonts (Inter)

---

**Made with ❤️ for modern VoIP management**
